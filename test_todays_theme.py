#!/usr/bin/env python3
"""
Test script to verify that get_todays_theme endpoint returns engagement_questions.
"""

import requests
import json
from typing import Optional

# Configuration
API_BASE_URL = "http://localhost:8204"
LOGIN_ENDPOINT = "/v1/auth/login"
TODAYS_THEME_ENDPOINT = "/v1/management/curated/get_todays_theme"

# Login credentials
LOGIN_CREDENTIALS = {
    "username": "admin",
    "password": "ipZT=8b50*bL",
    "client_id": "test"
}

def login_and_get_token() -> Optional[str]:
    """Login and get JWT token."""
    try:
        login_url = f"{API_BASE_URL}{LOGIN_ENDPOINT}"
        print(f"🔐 Logging in at: {login_url}")
        
        # Prepare form data for OAuth2 format
        form_data = {
            'username': LOGIN_CREDENTIALS['username'],
            'password': LOGIN_CREDENTIALS['password'],
            'client_id': LOGIN_CREDENTIALS['client_id']
        }
        
        response = requests.post(login_url, data=form_data)
        
        if response.status_code == 200:
            data = response.json()
            token = data.get("access_token")
            if token:
                print("✅ Login successful!")
                return token
            else:
                print("❌ Login failed: No access token in response")
                return None
        else:
            print(f"❌ Login failed with status {response.status_code}")
            print(f"Error: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_todays_theme(token: str):
    """Test the get_todays_theme endpoint."""
    try:
        todays_theme_url = f"{API_BASE_URL}{TODAYS_THEME_ENDPOINT}"
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        print(f"🎯 Testing get_todays_theme at: {todays_theme_url}")
        
        response = requests.get(todays_theme_url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ get_todays_theme successful!")
            print(f"📊 Response structure:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # Check if engagement_questions are present
            if "data" in data and "engagement_questions" in data["data"]:
                engagement_questions = data["data"]["engagement_questions"]
                print(f"\n🎉 Engagement questions found: {len(engagement_questions)} questions")
                for i, question in enumerate(engagement_questions, 1):
                    print(f"  {i}. {question.get('text', 'No text')}")
                    print(f"     EN: {question.get('text_en', 'No English text')}")
                    print(f"     Type: {question.get('type', 'No type')}")
            else:
                print("⚠️  No engagement_questions found in response")
            
            return data
        else:
            print(f"❌ get_todays_theme failed with status {response.status_code}")
            print(f"Error: {response.text}")
            return None
    except Exception as e:
        print(f"❌ get_todays_theme error: {e}")
        return None

def main():
    """Main test function."""
    print("🚀 Testing get_todays_theme endpoint for engagement_questions...")
    
    # Step 1: Login and get token
    token = login_and_get_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return
    
    # Step 2: Test get_todays_theme endpoint
    result = test_todays_theme(token)
    
    if result:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed!")

if __name__ == "__main__":
    main()
