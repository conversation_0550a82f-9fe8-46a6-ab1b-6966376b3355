[project]
name = "nepali-app"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "fastapi[standard]>=0.115.12",
    "argon2-cffi>=23.1.0",
    "bcrypt>=4.3.0",
    "fastapi-cli>=0.0.7",
    "google-genai>=1.10.0",
    "google-generativeai>=0.8.4",
    "httpx>=0.28.1",
    "minio>=7.2.15",
    "pillow>=11.1.0",
    "pyjwt>=2.10.1",
    "pymongo>=4.11.2",
    "pytest>=8.3.5",
    "pytest-asyncio>=0.25.3",
    "pytest-cov>=6.0.0",
    "pytest-mongodb>=2.4.0",
    "python-dotenv>=1.0.1",
    "python-multipart>=0.0.20",
    "requests>=2.32.3",
    "shutup>=0.2.0",
    "tabulate>=0.9.0",
    "uvicorn>=0.34.0",
    "psutil>=5.9.8",
    "opencv-python>=*********",
    "mss>=10.0.0",
    "prometheus-client>=0.21.1",
    "aiohttp>=3.11.18",
    "google-auth>=2.28.2",
    "email-validator>=2.1.1",
    "vertexai>=1.71.1",
    "redis>=5.0.0",
    "python-socketio>=5.13.0",
    "pydub>=0.25.1",
    "edge-tts>=7.0.2",
]

[dependency-groups]
dev = [
    "ipykernel>=6.29.5",
]
