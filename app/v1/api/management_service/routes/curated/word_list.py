from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, List, Any, Optional
from bson import ObjectId
from app.shared.security import get_tenant_info
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.v1.schema.pagination import PaginationResponse
from app.shared.utils.mongodb import convert_object_ids
from urllib.parse import urlparse


loggers = setup_new_logging(__name__)

router = APIRouter(
)
@router.get("/word-list", response_model=PaginationResponse[Dict[str, Any]])
async def word_list(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    start_date: Optional[str] = Query(None, description="Start date filter (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date filter (YYYY-MM-DD)"),
    sort_order: Optional[str] = Query("desc", description="Sort order: asc or desc"),
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> PaginationResponse[Dict[str, Any]]:
    """
    Get a unique word list across all task items.
    """
    try:
        # Build base filter
        filter_query = {
            "user_id": ObjectId(user_tenant.user.id),
            "type": {"$ne": "speak_word"},
        }

        # Date filter
        if start_date or end_date:
            date_filter = {}
            if start_date:
                try:
                    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                    date_filter["$gte"] = start_dt
                except ValueError:
                    pass
            if end_date:
                try:
                    end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(hour=23, minute=59, second=59)
                    date_filter["$lte"] = end_dt
                except ValueError:
                    pass
            if date_filter:
                filter_query["created_at"] = date_filter

        # Get all matching documents (only necessary fields)
        cursor = user_tenant.async_db.task_items.find(
            filter_query,
            {"question.options_metadata": 1}
        )
        all_docs = await cursor.to_list(length=None)

        # Build unique word list
        seen_texts = set()
        unique_words = []

        for doc in all_docs:
            options = doc.get("question", {}).get("options_metadata", {}).values()
            for opt in options:
                text = opt.get("text")
                if text and text not in seen_texts:
                    try:
                        seen_texts.add(text)
                        unique_words.append({
                            "text": text,
                            "audio_url": opt.get("audio_url"),
                            "object_name": opt.get("file_info", {}).get("object_name", None)
                        })
                    except Exception as e:
                        loggers.warning(f"Failed to process option {opt}: {e}")
        # Sort if required
        reverse = sort_order == "desc"
        unique_words.sort(key=lambda x: x["text"], reverse=reverse)

        # Paginate result
        total = len(unique_words)
        start_index = (page - 1) * limit
        end_index = start_index + limit
        paginated_words = unique_words[start_index:end_index]

        # Replace audio URLs
        for word in paginated_words:
            url = word.get("audio_url", "")
            path = urlparse(url).path
            parts = path.lstrip('/').split('/', 1)
            obj_name = parts[1] if len(parts) > 1 else None
            if obj_name:
                try:
                    new_url = user_tenant.minio.get_url(obj_name)
                    word["audio_url"] = new_url
                except Exception as e:
                    loggers.warning(f"Failed to generate new URL for {obj_name}: {e}")

        return PaginationResponse[Dict[str, Any]](
            data=convert_object_ids(paginated_words),
            meta={
                "page": page,
                "limit": limit,
                "total": total,
                "total_pages": (total + limit - 1) // limit
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error in /word-list endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
