"""
Routes for curated content management - themes and content sets.
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, Query
from fastapi.responses import JSONResponse
from typing import Dict, List, Any, Optional
from bson import ObjectId
from pymongo.errors import PyMongoError
import json
import random
from datetime import datetime, timezone


from app.shared.security import get_tenant_info
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.shared.api_response import APIResponse, ResponseMetadata
from app.v1.schema.pagination import PaginationResponse
from app.shared.utils.mongodb import convert_object_ids
from app.shared.db_enums import TaskStatus, InputType
from app.v1.api.management_service.routes.curated.questions import convert_curated_to_task_set

# Configure logging
loggers = setup_new_logging(__name__)

router = APIRouter()


class CuratedContentManager:
    """Manager for curated content operations."""
    
    def __init__(self, current_user: UserTenantDB):
        """Initialize the curated content manager."""
        self.tenant_db = current_user.async_db
        self.current_user = current_user

    async def get_all_themes(
        self,
        page: int = 1,
        limit: int = 50,
        search: Optional[str] = None,
        category: Optional[str] = None,
        is_active: Optional[bool] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        sort_order: Optional[str] = "desc"
    ) -> Dict[str, Any]:
        """Get all themes with optional filtering and pagination."""
        try:
            from datetime import datetime

            # Build filter query
            filter_query = {}

            if search:
                filter_query["$or"] = [
                    {"name": {"$regex": search, "$options": "i"}},
                    {"name_en": {"$regex": search, "$options": "i"}},
                    {"description": {"$regex": search, "$options": "i"}},
                    {"description_en": {"$regex": search, "$options": "i"}}
                ]

            if category:
                filter_query["category"] = category

            if is_active is not None:
                filter_query["is_active"] = is_active

            # Date filtering
            if start_date or end_date:
                date_filter = {}
                if start_date:
                    try:
                        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                        date_filter["$gte"] = start_dt
                    except ValueError:
                        pass  # Invalid date format, skip
                if end_date:
                    try:
                        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                        # Add 23:59:59 to include the entire end date
                        end_dt = end_dt.replace(hour=23, minute=59, second=59)
                        date_filter["$lte"] = end_dt
                    except ValueError:
                        pass  # Invalid date format, skip

                if date_filter:
                    filter_query["created_at"] = date_filter

            # Calculate skip value
            skip = (page - 1) * limit

            # Get total count
            total = await self.tenant_db.themes.count_documents(filter_query)

            # Determine sort direction
            sort_direction = -1 if sort_order == "desc" else 1

            # Get themes with pagination
            cursor = self.tenant_db.themes.find(filter_query).skip(skip).limit(limit).sort("created_at", sort_direction)
            themes = await cursor.to_list(length=limit)

            # Convert ObjectIds to strings
            themes = convert_object_ids(themes)

            # Calculate pagination metadata
            total_pages = (total + limit - 1) // limit if total > 0 else 0

            return {
                "data": themes,
                "meta": {
                    "page": page,
                    "limit": limit,
                    "total": total,
                    "total_pages": total_pages
                }
            }

        except PyMongoError as e:
            loggers.error(f"Database error in get_all_themes: {e}")
            raise HTTPException(status_code=500, detail="Database error occurred")
        except Exception as e:
            loggers.error(f"Unexpected error in get_all_themes: {e}")
            raise HTTPException(status_code=500, detail="An unexpected error occurred")

    async def get_theme_content_sets(
        self,
        theme_id: str,
        page: int = 1,
        limit: int = 20,
        difficulty_level: Optional[int] = None,
        status: Optional[str] = None,
        gentype: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get all content sets for a specific theme."""
        try:
            # Validate theme_id
            if not ObjectId.is_valid(theme_id):
                raise HTTPException(status_code=400, detail="Invalid theme ID format")

            # Check if theme exists
            theme = await self.tenant_db.themes.find_one({"_id": ObjectId(theme_id)})
            if not theme:
                raise HTTPException(status_code=404, detail="Theme not found")

            # Build filter query for content sets
            filter_query = {"theme_id": ObjectId(theme_id)}

            if difficulty_level:
                filter_query["difficulty_level"] = difficulty_level

            if status:
                filter_query["status"] = status

            if gentype:
                filter_query["gentype"] = gentype

            # Calculate skip value
            skip = (page - 1) * limit

            # Get total count
            total = await self.tenant_db.curated_content_set.count_documents(filter_query)

            # Get content sets with pagination
            cursor = self.tenant_db.curated_content_set.find(filter_query).skip(skip).limit(limit).sort("created_at", -1)
            content_sets = await cursor.to_list(length=limit)

            # Convert ObjectIds to strings and add theme info
            content_sets = convert_object_ids(content_sets)
            
            # Add theme information to each content set
            theme_info = convert_object_ids(theme)
            for content_set in content_sets:
                content_set["theme"] = {
                    "id": theme_info.get("_id", str(theme["_id"])),
                    "name": theme_info["name"],
                    "name_en": theme_info["name_en"],
                    "icon": theme_info["icon"],
                    "color": theme_info["color"],
                    "category": theme_info["category"]
                }

            # Calculate pagination metadata
            total_pages = (total + limit - 1) // limit if total > 0 else 0

            return {
                "data": content_sets,
                "meta": {
                    "page": page,
                    "limit": limit,
                    "total": total,
                    "total_pages": total_pages
                },
                "theme": convert_object_ids(theme)
            }

        except HTTPException:
            raise
        except PyMongoError as e:
            loggers.error(f"Database error in get_theme_content_sets: {e}")
            raise HTTPException(status_code=500, detail="Database error occurred")
        except Exception as e:
            loggers.error(f"Unexpected error in get_theme_content_sets: {e}")
            raise HTTPException(status_code=500, detail="An unexpected error occurred")


@router.get("/themes", response_model=PaginationResponse[Dict[str, Any]])
async def get_themes(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(50, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search in theme names and descriptions"),
    category: Optional[str] = Query(None, description="Filter by category"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    start_date: Optional[str] = Query(None, description="Start date filter (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date filter (YYYY-MM-DD)"),
    sort_order: Optional[str] = Query("desc", description="Sort order: asc or desc"),
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> PaginationResponse[Dict[str, Any]]:
    """
    Get all available themes with optional filtering and pagination.
    
    This endpoint returns all Nepal-themed categories available for curated content.
    Each theme contains information about cultural, geographical, historical, and other
    aspects of Nepal.
    
    Query Parameters:
    - page: Page number (default: 1)
    - limit: Items per page (default: 50, max: 100)
    - search: Search in theme names and descriptions
    - category: Filter by category (culture, geography, history, etc.)
    - is_active: Filter by active status (true/false)
    """
    try:
        manager = CuratedContentManager(user_tenant)
        result = await manager.get_all_themes(
            page=page,
            limit=limit,
            search=search,
            category=category,
            is_active=is_active,
            start_date=start_date,
            end_date=end_date,
            sort_order=sort_order
        )
        
        return PaginationResponse[Dict[str, Any]](
            data=result["data"],
            meta=result["meta"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error in get_themes endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/themes/{theme_id}", response_model=PaginationResponse[Dict[str, Any]])
async def get_theme_content_sets(
    theme_id: str,
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    difficulty_level: Optional[int] = Query(None, ge=1, le=3, description="Filter by difficulty level (1=easy, 2=medium, 3=hard)"),
    status: Optional[str] = Query(None, description="Filter by status (pending, completed, etc.)"),
    gentype: Optional[str] = Query(None, description="Filter by generation type (primary, follow_up, etc.)"),
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> PaginationResponse[Dict[str, Any]]:
    """
    Get all content sets for a specific theme.
    
    This endpoint returns all curated content sets associated with a particular theme.
    Each content set contains multiple task items (questions) related to the theme.
    
    Path Parameters:
    - theme_id: The ID of the theme to get content sets for
    
    Query Parameters:
    - page: Page number (default: 1)
    - limit: Items per page (default: 20, max: 100)
    - difficulty_level: Filter by difficulty level (1=easy, 2=medium, 3=hard)
    - status: Filter by status (pending, completed, etc.)
    """
    try:
        manager = CuratedContentManager(user_tenant)
        result = await manager.get_theme_content_sets(
            theme_id=theme_id,
            page=page,
            limit=limit,
            difficulty_level=difficulty_level,
            status=status,
            gentype=gentype
        )
        
        return PaginationResponse[Dict[str, Any]](
            data=result["data"],
            meta=result["meta"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error in get_theme_content_sets endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/filtered", response_model=PaginationResponse[Dict[str, Any]])
async def get_filtered_content_sets(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    theme_id: Optional[str] = Query(None, description="Filter by theme ID"),
    difficulty_level: Optional[int] = Query(None, ge=1, le=3, description="Filter by difficulty level (1=easy, 2=medium, 3=hard)"),
    status: Optional[str] = Query(None, description="Filter by status (pending, completed, etc.)"),
    gentype: Optional[str] = Query(None, description="Filter by generation type (primary, follow_up, etc.)"),
    start_date: Optional[str] = Query(None, description="Start date filter (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date filter (YYYY-MM-DD)"),
    sort_order: Optional[str] = Query("desc", description="Sort order: asc or desc"),
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> PaginationResponse[Dict[str, Any]]:
    """
    Get filtered content sets across all themes or for a specific theme.

    This endpoint allows filtering content sets by various criteria including
    theme, difficulty level, status, and generation type.

    Query Parameters:
    - page: Page number (default: 1)
    - limit: Items per page (default: 20, max: 100)
    - theme_id: Filter by specific theme ID (optional)
    - difficulty_level: Filter by difficulty level (1=easy, 2=medium, 3=hard)
    - status: Filter by status (pending, completed, etc.)
    - gentype: Filter by generation type (primary, follow_up, etc.)
    """
    try:
        manager = CuratedContentManager(user_tenant)

        # Build filter query
        filter_query = {}

        if theme_id:
            if not ObjectId.is_valid(theme_id):
                raise HTTPException(status_code=400, detail="Invalid theme ID format")
            filter_query["theme_id"] = ObjectId(theme_id)

        if difficulty_level:
            filter_query["difficulty_level"] = difficulty_level

        if status:
            filter_query["status"] = status

        if gentype:
            filter_query["gentype"] = gentype

        # Date filtering
        if start_date or end_date:
            from datetime import datetime
            date_filter = {}
            if start_date:
                try:
                    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                    date_filter["$gte"] = start_dt
                except ValueError:
                    pass  # Invalid date format, skip
            if end_date:
                try:
                    end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                    # Add 23:59:59 to include the entire end date
                    end_dt = end_dt.replace(hour=23, minute=59, second=59)
                    date_filter["$lte"] = end_dt
                except ValueError:
                    pass  # Invalid date format, skip

            if date_filter:
                filter_query["created_at"] = date_filter

        # Calculate skip value
        skip = (page - 1) * limit

        # Get total count
        total = await manager.tenant_db.curated_content_set.count_documents(filter_query)

        # Determine sort direction
        sort_direction = -1 if sort_order == "desc" else 1

        # Get content sets with pagination
        cursor = manager.tenant_db.curated_content_set.find(filter_query).skip(skip).limit(limit).sort("created_at", sort_direction)
        content_sets = await cursor.to_list(length=limit)

        # Convert ObjectIds to strings
        content_sets = convert_object_ids(content_sets)

        # Calculate pagination metadata
        total_pages = (total + limit - 1) // limit if total > 0 else 0

        return PaginationResponse[Dict[str, Any]](
            data=content_sets,
            meta={
                "page": page,
                "limit": limit,
                "total": total,
                "total_pages": total_pages
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error in get_filtered_content_sets endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/theme/{theme_id}")
async def get_theme_details(
    theme_id: str,
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> APIResponse[Dict[str, Any]]:
    """
    Get detailed information about a specific theme.
    
    This endpoint returns complete information about a theme including
    its metadata, statistics, and related content summary.
    
    Path Parameters:
    - theme_id: The ID of the theme to get details for
    """
    try:
        manager = CuratedContentManager(user_tenant)
        
        # Validate theme_id
        if not ObjectId.is_valid(theme_id):
            raise HTTPException(status_code=400, detail="Invalid theme ID format")

        # Get theme details
        theme = await manager.tenant_db.themes.find_one({"_id": ObjectId(theme_id)})
        if not theme:
            raise HTTPException(status_code=404, detail="Theme not found")

        # Get content sets count for this theme
        content_sets_count = await manager.tenant_db.curated_content_set.count_documents(
            {"theme_id": ObjectId(theme_id)}
        )

        # Get content items count for this theme
        content_items_count = await manager.tenant_db.curated_content_items.count_documents(
            {"question.metadata.theme_id": theme_id}
        )

        # Convert ObjectIds and add statistics
        theme_data = convert_object_ids(theme)
        theme_data["statistics"] = {
            "total_content_sets": content_sets_count,
            "total_content_items": content_items_count,
            "average_items_per_set": round(content_items_count / content_sets_count, 1) if content_sets_count > 0 else 0
        }

        return APIResponse[Dict[str, Any]](
            success=True,
            data=theme_data,
            message="Theme details retrieved successfully",
            metadata=ResponseMetadata(
                timestamp=None,
                request_id=None
            )
        )
        
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error in get_theme_details endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/filter/curated", response_model=APIResponse[Dict[str, Any]])
async def get_filter_options(
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> APIResponse[Dict[str, Any]]:
    """
    Get dynamic filter options for the /filtered endpoint.
    Returns:
    - Themes
    - Status values
    - Gentype values
    - Difficulty levels
    """
    try:
        manager = CuratedContentManager(user_tenant)

        # 🎯 Get all active themes
        themes_cursor = manager.tenant_db.themes.find(
            {"is_active": True},
            {"_id": 1, "name": 1, "name_en": 1}
        ).sort("name", 1)
        themes = await themes_cursor.to_list(length=None)

        theme_options = [
            {
                "id": str(theme["_id"]),
                "name": theme["name"],
                "name_en": theme.get("name_en", theme["name"])
            }
            for theme in themes
        ]

        # 🧠 One aggregation to fetch status, gentype, and difficulty_level
        pipeline = [
            {
                "$facet": {
                    "status_values": [
                        {"$group": {"_id": "$status"}},
                        {"$match": {"_id": {"$ne": None}}},
                        {"$sort": {"_id": 1}}
                    ],
                    "gentype_values": [
                        {"$group": {"_id": "$gentype"}},
                        {"$match": {"_id": {"$ne": None}}},
                        {"$sort": {"_id": 1}}
                    ],
                    "difficulty_levels": [
                        {"$group": {"_id": "$difficulty_level"}},
                        {"$match": {"_id": {"$ne": None}}},
                        {"$sort": {"_id": 1}}
                    ]
                }
            }
        ]

        facet_result = await(await user_tenant.async_db.curated_content_set.aggregate(pipeline)).to_list(length=1)
        facet_data = facet_result[0] if facet_result else {}

        # Get date range from content sets
        date_range_pipeline = [
            {
                "$group": {
                    "_id": None,
                    "earliest_date": {"$min": "$created_at"},
                    "latest_date": {"$max": "$created_at"}
                }
            }
        ]
        date_range_cursor = user_tenant.async_db.curated_content_set.aggregate(date_range_pipeline)
        date_range_result = await(await date_range_cursor).to_list(length=1)

        date_info = {}
        if date_range_result:
            earliest = date_range_result[0].get("earliest_date")
            latest = date_range_result[0].get("latest_date")
            if earliest and latest:
                date_info = {
                    "earliest_date": earliest.strftime("%Y-%m-%d"),
                    "latest_date": latest.strftime("%Y-%m-%d"),
                    "format": "YYYY-MM-DD",
                    "quick_filters": [
                        {"label": "Last 7 days", "days": 7},
                        {"label": "Last 30 days", "days": 30},
                        {"label": "Last 90 days", "days": 90},
                        {"label": "This year", "type": "year"}
                    ]
                }

        filter_options = {
            "themes": theme_options,
            "status_values": [item["_id"] for item in facet_data.get("status_values", [])],
            "gentype_values": [item["_id"] for item in facet_data.get("gentype_values", [])],
            "difficulty_levels": [item["_id"] for item in facet_data.get("difficulty_levels", [])],
            "sort_options": ["asc", "desc"],
            "date_filter_info": date_info
        }

        return APIResponse[Dict[str, Any]](
            success=True,
            data=filter_options,
            message="Filter options retrieved successfully",
            metadata=ResponseMetadata(
                timestamp=None,
                request_id=None
            )
        )

    except Exception as e:
        loggers.error(f"Error in get_filter_options endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")




@router.get("/filters/themes", response_model=APIResponse[Dict[str, Any]])
async def get_filter_for_themes(
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> APIResponse[Dict[str, Any]]:
    """
    Get filter options for themes.

    This endpoint returns available filter values for themes including:
    - Available categories
    - Active status options
    """
    try:
        manager = CuratedContentManager(user_tenant)

        # Get distinct categories from themes
        category_pipeline = [
            {"$group": {"_id": "$category"}},
            {"$match": {"_id": {"$ne": None}}},
            {"$sort": {"_id": 1}}
        ]
        category_cursor = manager.tenant_db.themes.aggregate(category_pipeline)
        categories = [doc["_id"] for doc in await(await category_cursor).to_list(length=None)]

        # Active status options (boolean values)
        is_active_options = [True, False]

        # Get date range from themes
        date_range_pipeline = [
            {
                "$group": {
                    "_id": None,
                    "earliest_date": {"$min": "$created_at"},
                    "latest_date": {"$max": "$created_at"}
                }
            }
        ]
        date_range_cursor = manager.tenant_db.themes.aggregate(date_range_pipeline)
        date_range_result = await(await date_range_cursor).to_list(length=1)

        date_info = {}
        if date_range_result:
            earliest = date_range_result[0].get("earliest_date")
            latest = date_range_result[0].get("latest_date")
            if earliest and latest:
                date_info = {
                    "earliest_date": earliest.strftime("%Y-%m-%d"),
                    "latest_date": latest.strftime("%Y-%m-%d"),
                    "format": "YYYY-MM-DD",
                    "quick_filters": [
                        {"label": "Last 7 days", "days": 7},
                        {"label": "Last 30 days", "days": 30},
                        {"label": "Last 90 days", "days": 90},
                        {"label": "This year", "type": "year"}
                    ]
                }

        filter_options = {
            "categories": categories,
            "is_active_options": is_active_options,
            "sort_options": ["asc", "desc"],
            "date_filter_info": date_info
        }

        return APIResponse[Dict[str, Any]](
            success=True,
            data=filter_options,
            message="Themes filter options retrieved successfully",
            metadata=ResponseMetadata(
                timestamp=None,
                request_id=None
            )
        )

    except Exception as e:
        loggers.error(f"Error in get_filter_for_themes endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/get_todays_theme", response_model=APIResponse[Dict[str, Any]])
async def get_todays_theme(
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> APIResponse[Dict[str, Any]]:
    """
    Get today's theme - select random theme and random content set.
    Returns curated set ID, theme color, and title.
    """
    try:

        # check user's daily theme cache
        user_doc=await user_tenant.async_db.users.find_one({"_id": ObjectId(user_tenant.user.id)})
        todays_date=datetime.now(timezone.utc).strftime("%Y-%m-%d")
        todays_cache= user_doc.get("daily_theme_cache",{}).get(todays_date)
        if todays_cache:
            return APIResponse[Dict[str, Any]](
                success=True,
                data=todays_cache,
                message="Today's theme retrieved from cache",
                metadata=ResponseMetadata(timestamp=None, request_id=None)
            )
        manager = CuratedContentManager(user_tenant)

        # Get all active themes with color
        themes = await user_tenant.async_db.themes.find(
            {"is_active": True},
            {"_id": 1, "name": 1, "color": 1}
        ).to_list(length=None)
        if not themes:
            raise HTTPException(status_code=404, detail="No active themes found")
        


        # Select random theme
        selected_theme = random.choice(themes)
        print("Selected theme:", selected_theme)

        # Get content sets for this theme
        content_sets = await manager.tenant_db.curated_content_set.find(
            {"theme_id": selected_theme["_id"]},
            {"_id": 1, "title": 1,"title_en": 1,"engagement_questions": 1,"description_en": 1,"description": 1}
        ).to_list(length=None)

        if not content_sets:
            raise HTTPException(status_code=404, detail="No content sets found")

        # Select random content set
        selected_set = random.choice(content_sets)


        new_task:JSONResponse=await convert_curated_to_task_set(str(selected_set["_id"]),user_tenant)

        # Parse the JSONResponse content to get task_set_id
        task_response_data = json.loads(new_task.body.decode('utf-8'))
        task_set_id = task_response_data["task_set_id"]  # Direct access, no "data" wrapper

        # Create the response data structure
        response_data = {
            "curated_set_id": str(selected_set["_id"]),
            "task_set_id": task_set_id,
            "theme_color": selected_theme.get("color", "#4ECDC4"),
            "title": selected_set["title_en"],
            "theme_id": str(selected_theme["_id"]),
            "engagement_questions": selected_set.get("engagement_questions", []),
            "description_en": selected_set.get("description_en", ""),
            "description": selected_set.get("description", "")
        }
        # Update user's daily theme cache with the same data structure
        await user_tenant.async_db.users.update_one(
            {"_id": ObjectId(user_tenant.user.id)},
            {
                "$set": {
                    f"daily_theme_cache.{todays_date}": response_data
                }
            }
        )


        return APIResponse[Dict[str, Any]](
            success=True,
            data=response_data,
            message="Today's theme selected",
            metadata=ResponseMetadata(timestamp=None, request_id=None)
        )

    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error in get_todays_theme: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


