"""
Optimized Task Processor - STOR<PERSON> IMAGE FIRST PRIORITY
- First task (prioritized single choice) gets ONLY story image generated synchronously
- Story image MUST complete before response returns to client
- ALL other content (including first task's other content) runs in parallel
- Guarantees immediate response after first story image generation
- Clean, readable, optimized architecture
"""

import asyncio
from typing import Any, Dict, List
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)

# Import generation functions from v2 socket service
from app.v2.api.socket_service_v2.generator.imagen import generate_image
from app.v2.api.socket_service_v2.generator.audiogen import generate_audio


async def process_tasks_optimized(
    current_user: Any,
    task_set_id: str,
    prioritized_tasks: List[Dict[str, Any]],
    item_ids: List[Any],
    socketio_server: Any = None,
    use_background_tasks: bool = True
) -> None:
    """
    EXPLICIT 3-PHASE PROCESSING:
    PHASE 1: Generate ONLY first task's story image (BLOCKING - must complete)
    PHASE 2: Return response immediately
    PHASE 3: Process ALL other content (AFTER response returned)

    Args:
        use_background_tasks: If True (socket), use asyncio.create_task for parallel processing
                             If False (HTTP), process remaining tasks in real-time after return
    """
    try:
        if not prioritized_tasks:
            logger.warning("No tasks to process")
            return

        logger.info(f"🚀 EXPLICIT 3-PHASE PROCESSING: {len(prioritized_tasks)} tasks")

        # PHASE 1: FIRST TASK STORY IMAGE ONLY (BLOCKING)
        first_task = prioritized_tasks[0]
        first_task_type = first_task.get('type', '')
        first_story_data = first_task.get('story', {})
        first_story_image = first_story_data.get('image', '')

        logger.info(f"� PHASE 1: First task validation - type='{first_task_type}', has_story_image={bool(first_story_image)}")

        if first_task_type == 'single_choice' and first_story_image:
            logger.info(f"✅ OPTIMAL: First task is single_choice with story image")
        else:
            logger.warning(f"⚠️  SUBOPTIMAL: First task type='{first_task_type}', has_story_image={bool(first_story_image)}")

        # PHASE 1: Generate ONLY first task's story image (BLOCKING)
        logger.info("🎯 PHASE 1 START: Generating ONLY first task's story image (BLOCKING)")
        if first_story_data and first_story_image:
            await generate_story_image(
                current_user, task_set_id, first_task, 0, item_ids, first_story_image, socketio_server
            )
            logger.info("✅ PHASE 1 COMPLETE: First task story image generated successfully")
        else:
            logger.warning("⚠️  PHASE 1 SKIPPED: No story image in first task")

        # PHASE 2: Return response (this function completes here)
        logger.info("🎉 PHASE 2: RETURNING RESPONSE NOW - first story image ready")

        # PHASE 3: Start ALL other content (AFTER response)
        if use_background_tasks:
            # SOCKET MODE: Use background tasks for parallel processing
            logger.info("🚀 PHASE 3 START: Processing ALL other content in parallel (background tasks)")

            # Start first task's remaining content in parallel
            asyncio.create_task(
                process_first_task_remaining_content(
                    current_user, task_set_id, first_task, 0, item_ids, socketio_server, use_background_tasks=True
                )
            )

            # Start all remaining tasks in parallel
            if len(prioritized_tasks) > 1:
                for i, task in enumerate(prioritized_tasks[1:], 1):
                    asyncio.create_task(
                        process_single_task(current_user, task_set_id, task, i, item_ids, is_first_task=False, socketio_server=socketio_server)
                    )
                logger.info(f"🚀 PHASE 3: Started {len(prioritized_tasks) - 1} remaining tasks + first task's other content in parallel")
            else:
                logger.info("🚀 PHASE 3: Started first task's remaining content in parallel")

            logger.info("🏁 EXPLICIT 3-PHASE COMPLETE: Response returned, background processing continues")
        else:
            # HTTP MODE: Process remaining tasks in real-time (not background)
            logger.info("🚀 PHASE 3 START: Processing ALL other content in real-time (HTTP mode)")

            # Process first task's remaining content in real-time
            await process_first_task_remaining_content(
                current_user, task_set_id, first_task, 0, item_ids, socketio_server, use_background_tasks=False
            )

            # Process all remaining tasks in real-time
            if len(prioritized_tasks) > 1:
                for i, task in enumerate(prioritized_tasks[1:], 1):
                    await process_single_task(current_user, task_set_id, task, i, item_ids, is_first_task=False, socketio_server=socketio_server)
                logger.info(f"🚀 PHASE 3: Completed {len(prioritized_tasks) - 1} remaining tasks + first task's other content in real-time")
            else:
                logger.info("🚀 PHASE 3: Completed first task's remaining content in real-time")

            logger.info("🏁 EXPLICIT 3-PHASE COMPLETE: Response returned, real-time processing completed")

    except Exception as e:
        logger.error(f"❌ 3-Phase processing failed: {e}")
        raise


async def process_first_task_remaining_content(
    current_user: Any,
    task_set_id: str,
    task: Dict[str, Any],
    task_index: int,
    item_ids: List[Any],
    socketio_server: Any = None,
    use_background_tasks: bool = True
) -> None:
    """
    Process the first task's remaining content (everything except story image) in parallel.
    This runs AFTER the story image has been generated and response returned.
    """
    try:
        task_type = task.get("type", "")
        question_data = task.get("question", {})

        logger.info(f"🚀 Processing first task's remaining content - type: {task_type}")

        remaining_content_tasks = []

        # Question image (for image identification tasks)
        if task_type in ["image_identification", "image_identify"]:
            keyword = question_data.get("answer_hint") or question_data.get("answer")
            if keyword:
                logger.info(f"🖼️  Task {task_index} - queuing question image for parallel processing: '{keyword}'")
                remaining_content_tasks.append(
                    generate_question_image(current_user, task_set_id, task, task_index, item_ids, keyword, socketio_server)
                )

        # Audio (for speak word tasks)
        if task_type in ["speak_word", "audio_identification", "pronunciation"]:
            keyword = question_data.get("answer_hint") or question_data.get("answer")
            if keyword:
                logger.info(f"🔊 Task {task_index} - queuing audio for parallel processing: '{keyword}'")
                remaining_content_tasks.append(
                    generate_audio_content(current_user, task_set_id, task, task_index, item_ids, keyword, socketio_server)
                )

        # Start remaining content based on mode
        if remaining_content_tasks:
            if use_background_tasks:
                # Background mode (socket)
                for content_task in remaining_content_tasks:
                    asyncio.create_task(content_task)
                logger.info(f"🎯 First task - started {len(remaining_content_tasks)} remaining content tasks in parallel (background)")
            else:
                # Real-time mode (HTTP)
                for content_task in remaining_content_tasks:
                    await content_task
                logger.info(f"🎯 First task - completed {len(remaining_content_tasks)} remaining content tasks in real-time")
        else:
            logger.info("🎯 First task - no remaining content to process")

    except Exception as e:
        logger.error(f"First task remaining content processing failed: {e}")


async def process_single_task(
    current_user: Any,
    task_set_id: str,
    task: Dict[str, Any],
    task_index: int,
    item_ids: List[Any],
    is_first_task: bool = False,
    socketio_server: Any = None
) -> None:
    """
    Process a single task with GUARANTEED priority logic:
    First task: ONLY story image generated synchronously (blocking), other content in parallel
    Other tasks: All content in parallel (non-blocking)
    """
    try:
        task_type = task.get("type", "")
        story_data = task.get("story", {})
        question_data = task.get("question", {})

        logger.info(f"Task {task_index} ({task_type}) - is_first_task: {is_first_task}")

        if is_first_task:
            # FIRST TASK: This should not be called directly in the optimized processor
            # The first task is handled by process_tasks_optimized function
            logger.warning(f"⚠️  Task {task_index} - process_single_task called with is_first_task=True, this should be handled by process_tasks_optimized")
            return
        else:
            # REMAINING TASKS: All content in parallel
            logger.debug(f"Task {task_index} ({task_type}) - processing all content in parallel")

            content_tasks = []

            # Question image (for image identification tasks)
            if task_type in ["image_identification", "image_identify"]:
                question_data = task.get("question", {})
                keyword = question_data.get("answer_hint") or question_data.get("answer")
                if keyword:
                    content_tasks.append(
                        generate_question_image(current_user, task_set_id, task, task_index, item_ids, keyword, socketio_server)
                    )

            # Story image
            if story_data and story_data.get("image"):
                content_tasks.append(
                    generate_story_image(current_user, task_set_id, task, task_index, item_ids, story_data.get("image"), socketio_server)
                )

            # Audio (for speak word tasks)
            if task_type in ["speak_word", "audio_identification", "pronunciation"]:
                question_data = task.get("question", {})
                keyword = question_data.get("answer_hint") or question_data.get("answer")
                if keyword:
                    content_tasks.append(
                        generate_audio_content(current_user, task_set_id, task, task_index, item_ids, keyword, socketio_server)
                    )

            # Start all content generations in parallel (don't wait)
            if content_tasks:
                for content_task in content_tasks:
                    asyncio.create_task(content_task)
                logger.info(f"Task {task_index} - started {len(content_tasks)} parallel generations")

    except Exception as e:
        logger.error(f"Task {task_index} failed: {e}")
        if socketio_server:
            try:
                await socketio_server.emit('task_generation_error', {
                    'task_set_id': task_set_id,
                    'task_index': task_index,
                    'error': str(e),
                    'task_type': task.get("type", "")
                }, room=f"user_{current_user.user.id}")
            except Exception:
                pass


async def generate_story_image(
    current_user: Any,
    task_set_id: str,
    task: Dict[str, Any],
    task_index: int,
    item_ids: List[Any],
    keyword: str,
    socketio_server: Any = None
) -> None:
    """Generate story image and update database immediately."""
    try:
        logger.debug(f"Task {task_index} - generating story image: {keyword}")

        # Send start notification
        if socketio_server:
            try:
                await socketio_server.emit('image_generation_started', {
                    'task_set_id': task_set_id,
                    'task_index': task_index,
                    'keyword': keyword,
                    'image_type': 'story'
                }, room=f"user_{current_user.user.id}")
            except Exception:
                pass

        # Generate image with retry logic
        max_retries = 3
        for attempt in range(max_retries):
            try:
                result = await generate_image(current_user, keyword)
                if result and len(result) == 3:
                    _, file_info, meta = result
                    if file_info:
                        break
                    else:
                        logger.warning(f"Task {task_index} - attempt {attempt + 1}: no file_info for '{keyword}'")
                else:
                    logger.warning(f"Task {task_index} - attempt {attempt + 1}: invalid result for '{keyword}': {result}")

                if attempt < max_retries - 1:
                    await asyncio.sleep(1 * (attempt + 1))  # Exponential backoff

            except Exception as e:
                logger.error(f"Task {task_index} - attempt {attempt + 1}: error generating image for '{keyword}': {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1 * (attempt + 1))
                else:
                    return
        else:
            logger.error(f"Task {task_index} - all {max_retries} attempts failed for '{keyword}'")
            return

        # Update database immediately
        await update_task_with_image(current_user, task_index, item_ids, file_info, meta, "story")
        
        logger.info(f"✅ Task {task_index} - story image updated in DB: {keyword}")

        # Send completion notification
        if socketio_server:
            try:
                await socketio_server.emit('image_generation_completed', {
                    'task_set_id': task_set_id,
                    'task_index': task_index,
                    'keyword': keyword,
                    'image_type': 'story',
                    'file_info': file_info
                }, room=f"user_{current_user.user.id}")
            except Exception:
                pass

    except Exception as e:
        logger.error(f"Task {task_index} - story image generation failed: {e}")


async def generate_question_image(
    current_user: Any,
    task_set_id: str,
    task: Dict[str, Any],
    task_index: int,
    item_ids: List[Any],
    keyword: str,
    socketio_server: Any = None
) -> None:
    """Generate question image and update database immediately."""
    try:
        logger.debug(f"Task {task_index} - generating question image: {keyword}")

        # Generate image with retry logic
        max_retries = 3
        for attempt in range(max_retries):
            try:
                result = await generate_image(current_user, keyword)
                if result and len(result) == 3:
                    _, file_info, meta = result
                    if file_info:
                        break
                    else:
                        logger.warning(f"Task {task_index} - attempt {attempt + 1}: no file_info for '{keyword}'")
                else:
                    logger.warning(f"Task {task_index} - attempt {attempt + 1}: invalid result for '{keyword}': {result}")

                if attempt < max_retries - 1:
                    await asyncio.sleep(1 * (attempt + 1))

            except Exception as e:
                logger.error(f"Task {task_index} - attempt {attempt + 1}: error generating image for '{keyword}': {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1 * (attempt + 1))
                else:
                    return
        else:
            logger.error(f"Task {task_index} - all {max_retries} attempts failed for '{keyword}'")
            return

        # Update database immediately
        await update_task_with_image(current_user, task_index, item_ids, file_info, meta, "question")
        
        logger.info(f"✅ Task {task_index} - question image updated in DB: {keyword}")

    except Exception as e:
        logger.error(f"Task {task_index} - question image generation failed: {e}")


async def generate_audio_content(
    current_user: Any,
    task_set_id: str,
    task: Dict[str, Any],
    task_index: int,
    item_ids: List[Any],
    keyword: str,
    socketio_server: Any = None
) -> None:
    """Generate audio and update database immediately."""
    try:
        logger.debug(f"Task {task_index} - generating audio: {keyword}")

        # Generate audio with retry logic
        max_retries = 3
        for attempt in range(max_retries):
            try:
                result = await generate_audio(current_user, keyword)
                if result and len(result) == 3:
                    _, file_info, meta = result
                    if file_info:
                        break
                    else:
                        logger.warning(f"Task {task_index} - attempt {attempt + 1}: no file_info for '{keyword}'")
                else:
                    logger.warning(f"Task {task_index} - attempt {attempt + 1}: invalid result for '{keyword}': {result}")

                if attempt < max_retries - 1:
                    await asyncio.sleep(1 * (attempt + 1))

            except Exception as e:
                logger.error(f"Task {task_index} - attempt {attempt + 1}: error generating audio for '{keyword}': {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1 * (attempt + 1))
                else:
                    return
        else:
            logger.error(f"Task {task_index} - all {max_retries} attempts failed for '{keyword}'")
            return

        # Update database immediately
        await update_task_with_audio(current_user, task_index, item_ids, file_info, meta)
        
        logger.info(f"✅ Task {task_index} - audio updated in DB: {keyword}")

    except Exception as e:
        logger.error(f"Task {task_index} - audio generation failed: {e}")


async def update_task_with_image(
    current_user: Any,
    task_index: int,
    item_ids: List[Any],
    file_info: Dict,
    meta: Any,
    image_type: str
) -> None:
    """Update task item with image data."""
    if task_index >= len(item_ids):
        logger.error(f"Task {task_index} - index out of range")
        return

    task_item_id = item_ids[task_index]
    update_data = {}
    
    if image_type == "question":
        update_data["question.media_url"] = file_info.get("object_name", "")
        update_data["question.metadata"] = file_info
    elif image_type == "story":
        update_data["story.media_url"] = file_info.get("object_name", "")
        update_data["story.metadata"] = file_info

    # Add usage metadata
    if meta:
        meta_dict = meta.model_dump() if hasattr(meta, 'model_dump') else (meta.dict() if hasattr(meta, 'dict') else dict(meta))
        update_data["usage"] = {
            **meta_dict,
            "type": "image",
            "image_type": image_type
        }

    if update_data:
        result = await current_user.async_db.task_items.update_one(
            {"_id": task_item_id},
            {"$set": update_data}
        )
        if result.modified_count == 0:
            logger.warning(f"Task {task_index} - failed to update {image_type} image")


async def update_task_with_audio(
    current_user: Any,
    task_index: int,
    item_ids: List[Any],
    file_info: Dict,
    meta: Any
) -> None:
    """Update task item with audio data."""
    if task_index >= len(item_ids):
        logger.error(f"Task {task_index} - index out of range")
        return

    task_item_id = item_ids[task_index]
    update_data = {
        "question.media_url": file_info.get("object_name", ""),
        "question.metadata": file_info
    }

    # Add usage metadata
    if meta:
        meta_dict = meta.model_dump() if hasattr(meta, 'model_dump') else (meta.dict() if hasattr(meta, 'dict') else dict(meta))
        update_data["usage"] = {
            **meta_dict,
            "type": "audio"
        }

    result = await current_user.async_db.task_items.update_one(
        {"_id": task_item_id},
        {"$set": update_data}
    )
    if result.modified_count == 0:
        logger.warning(f"Task {task_index} - failed to update audio")
