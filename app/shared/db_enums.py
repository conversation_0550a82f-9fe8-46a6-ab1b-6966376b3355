"""
Enums for database collections, fields, and statuses.
"""
from enum import Enum, auto

class CollectionName(str, Enum):
    """Enum for database collection names."""
    TASK_SETS = "task_sets"
    TASK_ITEMS = "task_items"
    TASK_HISTORY = "task_history"
    STORY_STEPS = "story_steps"
    USERS = "users"
    ROLES = "roles"
    CONFIG = "config"
    PROMPTS = "prompts"
    editor_prompts = "editor_prompts"
    MEDIA = "media"


class TaskStatus(str, Enum):
    """Enum for task status values."""
    ALL = "all"
    PENDING = "pending"
    COMPLETED = "completed"
    SKIPPED = "skipped"
    EXPIRED = "expired"

class GenerationType(str, Enum):
    """Enum for generation types."""
    PRIMARY = "primary"
    FOLLOW_UP = "follow_up"
    
class TaskResult(str, Enum):
    """Enum for task result values."""
    CORRECT = "correct"
    INCORRECT = "incorrect"
    PARTIAL = "partial"
    UNANSWERED = "unanswered"


class VerificationStatus(str, Enum):
    """Enum for verification status values."""
    PENDING = "pending"
    VERIFIED = "verified"
    REJECTED = "rejected"


class InputType(str, Enum):
    """Enum for input types."""
    TEXT = "text"
    AUDIO = "audio"
    VIDEO = "video"
    TRANSCRIPTION = "transcription"
    IDENTIFICATION = "identification"


class QuizType(str, Enum):
    """Enum for quiz types."""
    MULTIPLE_CHOICE = "multiple_choice"
    IMAGE_IDENTIFICATION = "image_identification"
    SPEAK_WORD = "speak_word"
    ANSWER_IN_WORD = "answer_in_word"
    SINGLE_CHOICE = "single_choice"
    TRUE_FALSE = "true_false"
    WORD_IDENTIFICATION = "word_identification"


class ScoreValue(int, Enum):
    """Enum for score values."""
    MULTIPLE_CHOICE = 10
    SINGLE_CHOICE = 10
    IMAGE_IDENTIFICATION = 10
    SPEAK_WORD = 15
    ANSWER_IN_WORD = 20
    BONUS = 5
    PENALTY = -2

class MediaType(str, Enum):
    """Enum for media types."""
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    DOCUMENT = "document"
    OTHER = "other"


class DifficultyLevel(int, Enum):
    """Enum for difficulty levels - stored as integers in DB, displayed as labels."""
    EASY = 1    # "easy"
    MEDIUM = 2  # "medium"
    HARD = 3    # "hard"