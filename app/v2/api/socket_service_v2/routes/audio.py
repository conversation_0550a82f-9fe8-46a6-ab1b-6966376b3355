"""
Audio Processing Routes for Socket Service V2

Collection-based audio processing that returns task_set_id and story_set_id
instead of individual items, with performance optimizations.
"""

from fastapi import APIRouter, Depends, HTTPException, File, UploadFile
from app.shared.models.user import UserTenantDB
from app.shared.security import get_tenant_info
from app.shared.utils.logger import setup_new_logging
from app.v2.api.socket_service_v2.generator.task_utils_v2 import (
    process_audio_with_prompt_maker_v2,
    save_task_collection_and_items_with_priority
)
from app.v2.api.socket_service_v2.generator.followup_simple import followup_generate
from app.v2.api.socket_service_v2.models.collections import CollectionResponse
import uuid

router = APIRouter(
    prefix="/audio",
    tags=["Audio Processing V2"],
)
logger = setup_new_logging(__name__)


@router.post("/process", response_model=CollectionResponse)
async def process_audio_v2(
    audio_file: UploadFile = File(...),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Process audio file and return collection IDs (V2).

    This V2 endpoint:
    1. Stores the uploaded audio file in MinIO
    2. Processes the audio using optimized AI to generate task and story collections
    3. Saves collections to the database with performance optimizations
    4. Returns task_set_id and story_set_id for the collections

    Key V2 Features:
    - Collection-based storage (multiple task sets per collection)
    - Performance optimized (choice questions exclude media)
    - Returns collection IDs instead of individual items
    - Parallel task and story generation

    Args:
        audio_file: Audio file upload (max 200MB)
        current_user: Current authenticated user

    Returns:
        CollectionResponse with:
        - task_set_id: ID of the created task collection
        - story_set_id: ID of the created story collection
        - optimization metadata

    Note:
        - Same file size limits as V1 (200MB)
        - Enhanced performance through media optimization
        - Collection-based storage for better organization
    """
    try:
        # Read audio data from uploaded file
        audio_data = await audio_file.read()
        logger.info(f"Processing audio file V2: {audio_file.filename}, size: {len(audio_data)} bytes")

        # Generate a session ID for this request
        session_id = str(uuid.uuid4())

        # Store audio in MinIO first
        audio_storage_info = None
        if current_user and current_user.minio:
            try:
                # Determine content type from file
                content_type = audio_file.content_type or "audio/wav"

                # Extract file extension from filename
                file_extension = None
                if audio_file.filename:
                    file_extension = "." + audio_file.filename.split(".")[-1] if "." in audio_file.filename else ".wav"
                else:
                    file_extension = ".wav"

                # Store audio file in MinIO
                audio_storage_info = current_user.minio.save_file(
                    data=audio_data,
                    user_id=current_user.user.id,
                    content_type=content_type,
                    folder="recordings_v2",  # Separate folder for V2
                    session_id=session_id,
                    file_extension=file_extension,
                    custom_filename=audio_file.filename
                )
                logger.info(f"📁 Audio stored in MinIO V2: {audio_storage_info.get('object_path')}")
            except Exception as e:
                logger.error(f"❌ Error storing audio in MinIO V2: {e}")
                audio_storage_info = None

        # Process audio with optimized V2 prompt maker for tasks
        logger.info("🔄 Starting V2 task generation...")
        tasks_data = await process_audio_with_prompt_maker_v2(
            current_user,
            audio_data,
            num_tasks=4
        )

        # Process audio with V2 story generator (parallel processing could be added here)
        logger.info("🔄 V2: Stories are generated together with tasks in prompt_maker_v2")
        # Stories are now included in the tasks_data from prompt_maker_v2
        story_data = {
            "story_set_id": tasks_data.get("task_set_id"),
            "status": "generated_with_tasks",
            "stories_count": len(tasks_data.get("stories", []))
        }

        # Initialize response
        response_data = {
            "session_id": session_id,
            "status": "completed",
            "task_set_id": None,
            "story_set_id": None,
            "task_collection_metadata": None,
            "story_collection_metadata": None
        }

        # Handle task collection creation
        if tasks_data.get("tasks"):
            logger.info("💾 Saving task collection...")
            save_result = await save_task_collection_and_items_with_priority(
                current_user,
                session_id,
                tasks_data,
                collection_id=None,  # Create new collection
                audio_storage_info=audio_storage_info,
                socketio_server=None,  # No socket server for HTTP
                use_background_tasks=True  # Enable background media generation
            )

            if save_result.get("status") == "success":
                response_data["task_set_id"] = save_result.get("task_set_id")
                response_data["task_collection_metadata"] = save_result.get("collection_metadata")
                logger.info(f"✅ Created task collection: {response_data['task_set_id']}")
            else:
                logger.error(f"Failed to save task collection: {save_result.get('error')}")
        else:
            logger.warning("No tasks generated from audio")

        # Handle story collection creation
        if story_data.get("story_set_id"):
            response_data["story_set_id"] = story_data.get("story_set_id")
            response_data["story_collection_metadata"] = story_data.get("collection_metadata")
            logger.info(f"✅ Created story collection: {response_data['story_set_id']}")
        else:
            logger.warning(f"No story collection created: {story_data.get('error', 'Unknown error')}")

        # Check if at least one collection was created
        if not response_data["task_set_id"] and not response_data["story_set_id"]:
            raise HTTPException(
                status_code=400,
                detail="No collections could be generated from the audio"
            )

        # Add processing metadata
        response_data["message"] = "Audio processed successfully with V2 optimizations"
        
        # Add optimization stats if available
        if tasks_data.get("optimization_stats"):
            if not response_data["task_collection_metadata"]:
                response_data["task_collection_metadata"] = {}
            response_data["task_collection_metadata"]["optimization_stats"] = tasks_data["optimization_stats"]

        logger.info(f"✅ V2 Audio processing completed for session {session_id}")
        return CollectionResponse(**response_data)

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error processing audio V2: {e}")
        raise HTTPException(status_code=500, detail="Failed to process audio with V2")


@router.get("/collections/{collection_id}/stats")
async def get_collection_stats(
    collection_id: str,
    collection_type: str,  # "task" or "story"
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get statistics for a specific collection.
    
    Args:
        collection_id: Collection identifier
        collection_type: Type of collection ("task" or "story")
        current_user: Current authenticated user
        
    Returns:
        Collection statistics and metadata
    """
    try:
        if collection_type == "task":
            collection = await current_user.async_db.task_collections.find_one(
                {"collection_id": collection_id, "user_id": str(current_user.user.id)}
            )
            collection_name = "task_collections"
        elif collection_type == "story":
            collection = await current_user.async_db.story_collections.find_one(
                {"collection_id": collection_id, "user_id": str(current_user.user.id)}
            )
            collection_name = "story_collections"
        else:
            raise HTTPException(status_code=400, detail="Invalid collection_type. Must be 'task' or 'story'")

        if not collection:
            raise HTTPException(status_code=404, detail=f"Collection {collection_id} not found")

        # Prepare stats response
        stats = {
            "collection_id": collection_id,
            "collection_type": collection_type,
            "user_id": collection["user_id"],
            "session_id": collection.get("session_id"),
            "status": collection.get("status"),
            "created_at": collection.get("created_at"),
            "updated_at": collection.get("updated_at"),
            "completed_at": collection.get("completed_at")
        }

        if collection_type == "task":
            stats.update({
                "total_task_sets": collection.get("total_task_sets", 0),
                "total_tasks": collection.get("total_tasks", 0),
                "task_set_ids": collection.get("task_set_ids", []),
                "optimization_metadata": collection.get("optimization_metadata", {}),
                "media_excluded_count": collection.get("media_excluded_count", 0),
                "optimized_for_performance": collection.get("optimized_for_performance", False)
            })
        else:  # story
            stats.update({
                "total_story_sets": collection.get("total_story_sets", 0),
                "total_stories": collection.get("total_stories", 0),
                "total_steps": collection.get("total_steps", 0),
                "completed_steps": collection.get("completed_steps", 0),
                "story_set_ids": collection.get("story_set_ids", [])
            })

        return stats

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting collection stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get collection statistics")

@router.post("/followup/{task_set_id}")
async def generate_followup_v2(
    task_set_id: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Generate followup tasks for a completed task set (V2).

    This V2 endpoint:
    1. Retrieves the completed task set
    2. Generates new followup tasks using a simplified AI model
    3. Saves the followup tasks to the database
    await followup_generate(task_set_id, current_user)
    return {"message": "Followup generation started"}
    """
    await followup_generate(task_set_id, current_user)
    return {"message": "Followup generation started"}
