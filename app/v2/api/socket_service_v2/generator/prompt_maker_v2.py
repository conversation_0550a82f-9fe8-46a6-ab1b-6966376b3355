"""
Optimized Prompt Maker V2 for Socket Service V2

This module provides an optimized version of the prompt maker that generates tasks
with performance optimizations:
- Choice-based questions (single_choice, multiple_choice) exclude images and audio
- Only interactive tasks (speak_word, story-based) include media
- Faster processing for text-only questions
- Same generation logic as original but with media optimization
"""

import os
import json
from typing import List, Dict, Any
from datetime import datetime, timezone
from google import genai
from google.genai import types
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from bson import ObjectId

# Configure logging
logger = setup_new_logging(__name__)

# V2: Generate media for ALL task types - no optimization exclusions


def parse_gemini_response(response_text: str) -> tuple[str, list]:
    """
    Parse Gemini response to extract title and tasks.

    Args:
        response_text: Raw JSON response from Gemini

    Returns:
        Tuple of (title, tasks_list)
    """
    try:
        import json
        data = json.loads(response_text)
        title = data.get("title", "Generated Tasks")
        tasks = data.get("tasks", [])
        return title, tasks
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse Gemini response: {e}")
        return "Generated Tasks", []
    except Exception as e:
        logger.error(f"Error parsing Gemini response: {e}")
        return "Generated Tasks", []


async def generate_nepali_quiz_prompt_v2(num_tasks, current_user: UserTenantDB = None):
    """
    Generate optimized Nepali quiz prompt for V2.
    
    This version includes instructions for media optimization based on task types.
    """
    try:
        # Fetch onboarding data age, topic and level from user onboarding
        prompt_data = await current_user.async_db.users.find_one(
            {"_id": ObjectId(current_user.user.id)}, 
            {"age": 1, "difficulty_level": 1, "preferred_topics": 1}
        )
        logger.info(f"Prompt data: {prompt_data}")
        
        difficulty_prompt = await current_user.async_db.prompts.find_one(
            {"name": "difficulty_guidance", "level": prompt_data.get("difficulty_level")}, 
            {"prompt": 1}
        )
        
        if prompt_data.get("preferred_topics"):
            topic_prompt = await current_user.async_db.prompts.find_one(
                {"name": "topic_guidance", "topic": "existing"}, 
                {"prompt": 1}
            )
            if topic_prompt:
                topic_prompt = topic_prompt.get("prompt", "").format(topic=prompt_data.get("preferred_topics"))
        else:
            topic_prompt = await current_user.async_db.prompts.find_one(
                {"name": "topic_guidance", "topic": "non_existing"}, 
                {"prompt": 1}
            )

        main_prompt = await current_user.async_db.prompts.find_one({"name": "main_prompt"})

        # Get the prompt text from main_prompt document
        prompt_text = main_prompt.get("prompt", "") if main_prompt else ""

        # Handle topic_prompt - it could be a string (formatted) or dict (from DB)
        if isinstance(topic_prompt, str):
            topic_text = topic_prompt
        else:
            topic_text = topic_prompt.get("prompt", "") if topic_prompt else ""

        # V2: Generate media for ALL tasks - no optimization restrictions
        media_instructions = """

V2 MEDIA GENERATION INSTRUCTIONS:
- Include media_url, images, and audio references for ALL task types
- Generate rich multimedia content for enhanced learning experience
- Include visual and audio elements wherever possible to improve engagement
"""

        formatted_prompt = prompt_text.format(
            num_tasks=num_tasks,
            difficulty_level=prompt_data.get("difficulty_level"),
            age=prompt_data.get("age"),
            topic_prompt=topic_text,
            difficulty_prompt=difficulty_prompt.get("prompt", "") if difficulty_prompt else ""
        ) + media_instructions

        return formatted_prompt.strip()
    except Exception as e:
        import traceback
        print(traceback.print_exc())
        print(f"Error generating prompt: {e}")
        raise


# V2: No optimization - generate media for ALL tasks


def convert_to_task_items_v2(gemini_tasks: List[Dict[str, Any]], user_difficulty_level: int = None) -> Dict[str, Any]:
    """
    Convert Gemini task format to expected task item format for task_utils_v2.py.

    V2 version: Generate media for ALL tasks - no optimization exclusions.
    """
    task_items = []
    story_items = []
    optimization_stats = {
        'total_tasks': len(gemini_tasks),
        'media_excluded_count': 0,
        'media_included_count': len(gemini_tasks),  # All tasks include media
        'optimization_applied': False,  # No optimization applied
        'stories_extracted': 0
    }

    # Use user difficulty level as integer directly (1=easy, 2=medium, 3=hard)
    difficulty_level_int = user_difficulty_level if user_difficulty_level in [1, 2, 3] else 2

    for task in gemini_tasks:
        try:
            # V2: No optimization - keep all tasks as-is with media

            # Extract basic task info
            title = task.get('title', 'Generated Task Item')
            task_type = task.get('type', 'single_choice')
            question_data = task.get('question', {})
            story_data = task.get('story', {})
            total_score = task.get('total_score', task.get('max_score', 10))
            complexity = task.get('complexity', difficulty_level_int)

            # Keep options as dictionary format
            options = question_data.get('options', {})
            if not isinstance(options, dict):
                options = {}

            # Build standardized correct answer structure with proper value types
            raw_answer = question_data.get('answer', '')
            if task_type in ["single_choice", "speak_word", "image_identification", "word_identification"]:
                correct_answer_data = {
                    "type": "single",
                    "value": raw_answer  # Single value as string (e.g., "a")
                }
            else:  # multiple_choice
                # Handle multiple choice answers - convert string to array if needed
                if isinstance(raw_answer, str):
                    # If Gemini returns "a,b" convert to ["a", "b"]
                    if "," in raw_answer:
                        answer_array = [item.strip() for item in raw_answer.split(",")]
                    else:
                        # If single answer like "a", convert to ["a"]
                        answer_array = [raw_answer.strip()]
                else:
                    # If already an array, use as-is
                    answer_array = raw_answer if isinstance(raw_answer, list) else [str(raw_answer)]

                correct_answer_data = {
                    "type": "multiple",
                    "value": answer_array  # Array of values (e.g., ["a", "b"])
                }

            # Create task item structure
            task_item = {
                "type": task_type,
                "title": title,
                "question": {
                    "text": question_data.get('text', ''),
                    "translated_text": question_data.get('translated_text', ''),
                    "options": options,
                    "answer_hint": question_data.get('answer_hint', ''),
                    "answer": question_data.get('answer', ''),
                    "media_url": question_data.get('media_url')  # Keep all media URLs
                },
                "correct_answer": correct_answer_data,  # Add standardized correct_answer
                "total_score": total_score,
                "difficulty_level": difficulty_level_int,
                "complexity": complexity,
                "answer": None,
                "scored": 0,
                # V2 metadata - no optimization applied
                "_v2_optimized": False,
                "_media_excluded": False
            }

            # Extract story data separately if present
            if story_data:
                story_item = {
                    "id": ObjectId(),
                    "stage": story_data.get('stage', 1),
                    "script": story_data.get('script', ''),
                    "image": story_data.get('image', ''),
                    "media_url": story_data.get('media_url', ''),
                    "metadata": story_data.get('metadata', {}),
                    "task_title": title,  # Link to related task
                    "task_type": task_type,
                    "created_at": datetime.now(timezone.utc)
                }

                # V2: Keep all story media - no optimization

                story_items.append(story_item)
                optimization_stats['stories_extracted'] += 1

            # Handle special cases for different task types
            if task_type == 'speak_word':
                if not task_item['question']['text']:
                    task_item['question']['text'] = ''
                task_item['question']['answer'] = question_data.get('answer_hint', '')

            task_items.append(task_item)

        except Exception as e:
            logger.error(f"Error converting task: {e}")
            continue

    logger.info(f"V2 Task Generation Stats: {optimization_stats}")

    return {
        "tasks": task_items,
        "stories": story_items,
        "optimization_stats": optimization_stats
    }


async def generate(audio_bytes, num_tasks, current_user: UserTenantDB = None) -> Dict[str, Any]:
    """
    Generate tasks from audio with media generation for ALL task types.

    V2 version: No optimization - generates images/audio for all tasks and stories.
    """
    try:
        # Check if API key is available
        api_key = os.environ.get("GEMINI_API_KEY")
        if not api_key:
            logger.error("❌ GEMINI_API_KEY not found in environment variables")
            return {"tasks": [], "optimization_stats": {}, "usage_metadata": {}}

        client = genai.Client(api_key=api_key)

        # Generate optimized prompt
        prompt_text = await generate_nepali_quiz_prompt_v2(
            num_tasks=num_tasks,
            current_user=current_user
        )
        logger.info(f"📝 Generated optimized V2 prompt for {num_tasks} tasks")

        model = "gemini-2.0-flash"
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_bytes(
                        mime_type="audio/ogg",
                        data=audio_bytes,
                    ),
                ],
            ),
        ]
        generate_content_config = types.GenerateContentConfig(
            temperature=0,
            response_mime_type="application/json",
            system_instruction=[
                types.Part.from_text(text=prompt_text),
            ],
        )

        logger.info(f"🤖 Calling Gemini API with {len(audio_bytes)} bytes of audio (V2 optimized)")

        # Collect all chunks from the streaming response
        full_response = ""
        chunk_count = 0
        usage_metadata = {}
        
        try:
            for chunk in client.models.generate_content_stream(
                model=model,
                contents=contents,
                config=generate_content_config,
            ):
                if chunk.text:
                    full_response += chunk.text
                    chunk_count += 1
                if chunk.usage_metadata:
                    usage_metadata = chunk.usage_metadata

            logger.info(f"📥 Received {chunk_count} chunks from Gemini API (V2), total response length: {len(full_response)}")
            
            if usage_metadata:
                logger.info(f"📊 V2 Audio processing metadata: {usage_metadata}")

        except Exception as api_error:
            logger.error(f"❌ Gemini API call failed (V2): {str(api_error)}")
            import traceback
            logger.error(f"API Error traceback: {traceback.format_exc()}")
            return {"tasks": [], "optimization_stats": {}, "usage_metadata": {}}

        # Parse and optimize the response
        if not full_response:
            logger.error("❌ Empty response from Gemini API (V2)")
            return {"tasks": [], "optimization_stats": {}, "usage_metadata": usage_metadata}

        try:
            # Get user's difficulty level
            user_profile = await current_user.async_db.users.find_one(
                {"_id": ObjectId(current_user.user.id)},
                {"difficulty_level": 1}
            )
            user_difficulty_level = user_profile.get("difficulty_level") if user_profile else 2

            # Parse response using v2 function
            from app.v2.api.socket_service_v2.generator.prompt_maker_v2 import parse_gemini_response
            title, gemini_tasks = parse_gemini_response(full_response)
            logger.info(f"📋 Parsed {len(gemini_tasks)} tasks from Gemini response with title: {title}")

            if not gemini_tasks:
                logger.warning("⚠️ No tasks found in Gemini response (V2)")
                return {"tasks": [], "optimization_stats": {}, "usage_metadata": usage_metadata}

            # Convert with V2 format (no optimization - all tasks get media)
            conversion_result = convert_to_task_items_v2(gemini_tasks, user_difficulty_level)
            task_items = conversion_result["tasks"]
            story_items = conversion_result["stories"]
            optimization_stats = conversion_result["optimization_stats"]

            logger.info(f"✅ V2 Generated {len(task_items)} tasks and {len(story_items)} stories - ALL will get media generated")

            return {
                "tasks": task_items,
                "stories": story_items,
                "title": title,
                "usage_metadata": usage_metadata,
                "optimization_stats": optimization_stats,
                "version": "v2"
            }

        except Exception as parse_error:
            logger.error(f"❌ Error parsing/converting response (V2): {str(parse_error)}")
            import traceback
            logger.error(f"Parse error traceback: {traceback.format_exc()}")
            return {"tasks": [], "optimization_stats": {}, "usage_metadata": usage_metadata}

    except Exception as e:
        logger.error(f"❌ Unexpected error in generate function (V2): {str(e)}")
        import traceback
        logger.error(f"Generate error traceback: {traceback.format_exc()}")
        return {"tasks": [], "optimization_stats": {}, "usage_metadata": {}}
