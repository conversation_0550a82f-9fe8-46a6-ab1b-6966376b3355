# Universal Media Generation Settings

Simple 5-setting global control for all media generation in V2 system.

## Quick Setup - 5 Simple Settings

Add this document to your tenant database `config` collection:

```json
{
  "name": "media_generation",
  "options_audio_enabled": false,
  "quiz_image_enabled": true,
  "quiz_audio_enabled": true,
  "story_image_enabled": true,
  "story_audio_enabled": true
}
```

## Control Individual Media Types

Turn OFF any media type by setting it to `false`:

```bash
# Turn OFF options audio
db.config.updateOne(
  {"name": "media_generation"},
  {"$set": {"options_audio_enabled": false}},
  {"upsert": true}
)

# Turn OFF quiz images
db.config.updateOne(
  {"name": "media_generation"},
  {"$set": {"quiz_image_enabled": false}},
  {"upsert": true}
)

# Turn OFF quiz audio
db.config.updateOne(
  {"name": "media_generation"},
  {"$set": {"quiz_audio_enabled": false}},
  {"upsert": true}
)

# Turn OFF story images
db.config.updateOne(
  {"name": "media_generation"},
  {"$set": {"story_image_enabled": false}},
  {"upsert": true}
)

# Turn OFF story audio
db.config.updateOne(
  {"name": "media_generation"},
  {"$set": {"story_audio_enabled": false}},
  {"upsert": true}
)
```

## Complete Setup

Add all settings at once:

```bash
db.config.insertOne({
  "name": "media_generation",
  "options_audio_enabled": false,
  "quiz_image_enabled": true,
  "quiz_audio_enabled": true,
  "story_image_enabled": true,
  "story_audio_enabled": true
})
```

## Audio Generation Fix

The V2 system now uses the same comprehensive Nepali prompt as V1:

- ✅ Fixed prompt lookup to use comprehensive Nepali TTS instructions
- ✅ All audio generation now uses "audio_prompt" consistently  
- ✅ Fallback prompt includes proper Nepali pronunciation guidelines
- ✅ Universal settings allow turning off options audio globally

## Testing

After updating the configuration, restart your application and test:

1. **Options Audio OFF**: No audio should be generated for task options
2. **Quiz Media Control**: Images/audio generation follows your settings
3. **Story Media Control**: Images/audio generation follows your settings

The system will log when media generation is skipped due to settings:

```
⏭️  TASK 6867815a62432084b838f268 | OPTIONS AUDIO: DISABLED GLOBALLY
```
