"""
Simplified WebSocket Helper for Socket Service V2
Essential WebSocket notifications only.
"""

from typing import Dict, Any, Optional
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


async def send_websocket_notification(current_user: UserTenantDB, session_id: str,
                                    event_type: str, data: Dict[str, Any]) -> bool:
    """Send simple WebSocket notification to client."""
    try:
        if hasattr(current_user, 'socketio') and current_user.socketio:
            notification = {
                "event": event_type,
                "session_id": session_id,
                "data": data
            }
            await current_user.socketio.emit(event_type, notification, room=session_id)
            logger.debug(f"📡 WEBSOCKET | {event_type.upper()} | SESSION: {session_id}")
            return True
        return False
    except Exception as e:
        logger.error(f"❌ WEBSOCKET ERROR | {event_type} | SESSION: {session_id} | ERROR: {e}")
        return False


