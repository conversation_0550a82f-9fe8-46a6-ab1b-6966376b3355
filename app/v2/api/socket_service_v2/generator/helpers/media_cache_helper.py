"""
Simplified Media Cache Helper for Socket Service V2
Simple media caching without complex search strategies.
"""

from datetime import datetime, timezone
from typing import Dict, Any, Optional
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.shared.db_enums import CollectionName

logger = setup_new_logging(__name__)


async def check_media_cache(current_user: UserTenantDB, keyword: str, media_type: str) -> Optional[Dict[str, Any]]:
    """Simple media cache check with multi-user support."""
    try:
        media_collection = current_user.db[CollectionName.MEDIA]
        cached_item = media_collection.find_one({
            "keyword": keyword.strip().lower(),
            "media_type": media_type
        })

        if cached_item:
            # Update user access tracking
            user_ids = cached_item.get("user_ids", [])
            if current_user.user.id not in user_ids:
                user_ids.append(current_user.user.id)
                media_collection.update_one(
                    {"_id": cached_item["_id"]},
                    {
                        "$set": {
                            "user_ids": user_ids,
                            "last_accessed_at": datetime.now(timezone.utc),
                            "last_accessed_by": current_user.user.id
                        }
                    }
                )
            else:
                # Just update last access time
                media_collection.update_one(
                    {"_id": cached_item["_id"]},
                    {
                        "$set": {
                            "last_accessed_at": datetime.now(timezone.utc),
                            "last_accessed_by": current_user.user.id
                        }
                    }
                )

            return cached_item.get("file_info")
        return None
    except Exception as e:
        logger.warning(f"Cache check failed: {e}")
        return None


async def save_media_cache(current_user: UserTenantDB, keyword: str, media_type: str,
                          file_text: str, file_info: Dict[str, Any], usage_metadata: Dict[str, Any]):
    """Simple media cache save with multi-user support."""
    try:
        media_collection = current_user.db[CollectionName.MEDIA]

        # Check if media already exists
        existing_media = media_collection.find_one({
            "keyword": keyword.strip().lower(),
            "media_type": media_type
        })

        if existing_media:
            # Add current user to user_ids list if not already present
            user_ids = existing_media.get("user_ids", [])
            if current_user.user.id not in user_ids:
                user_ids.append(current_user.user.id)

            media_collection.update_one(
                {"keyword": keyword.strip().lower(), "media_type": media_type},
                {
                    "$set": {
                        "user_ids": user_ids,
                        "last_accessed_at": datetime.now(timezone.utc),
                        "last_accessed_by": current_user.user.id
                    }
                }
            )
            logger.debug(f"Updated user access for cached {media_type} for keyword: {keyword}")
        else:
            # Create new media cache entry with user_ids list
            media_collection.update_one(
                {"keyword": keyword.strip().lower(), "media_type": media_type},
                {
                    "$set": {
                        "keyword": keyword.strip().lower(),
                        "media_type": media_type,
                        "file_text": file_text,
                        "file_info": file_info,
                        "usage_metadata": usage_metadata,
                        "created_at": datetime.now(timezone.utc),
                        "user_ids": [current_user.user.id],  # Store as list for multi-user access
                        "created_by": current_user.user.id,
                        "last_accessed_at": datetime.now(timezone.utc),
                        "last_accessed_by": current_user.user.id
                    }
                },
                upsert=True
            )
            logger.debug(f"Cached {media_type} for keyword: {keyword} with multi-user support")
    except Exception as e:
        logger.warning(f"Failed to save media cache: {e}")
