import random
from typing import Optional, Literal
import edge_tts
from edge_tts import VoicesManager

from app.shared.utils.logger import setup_new_logging

loggers = setup_new_logging(__name__)

async def generate_audio_bytes(
    text: str,
    gender: Optional[Literal["Male", "Female"]] = "Female",
    language: Optional[str] = "ne",
    locale: Optional[str] = None,
    rate: Optional[str] = "-10%"
) -> bytes | None:
    """
    Generates audio bytes from text using Edge TTS.
    Allows specifying gender, language, and locale for voice selection.

    Args:
        text (str): The text to convert to speech.
        gender (Optional[Literal["Male", "Female"]]): The desired gender for the voice.
        language (Optional[str]): The desired language (e.g., "en", "es", "fr").
        locale (Optional[str]): The desired locale (e.g., "en-US", "es-ES").

    Returns:
        bytes: The generated audio as MP3 bytes.

    Raises:
        ValueError: If no voice criteria are provided or no matching voices are found.
        Exception: For any other errors during audio generation.
    """
    voices_manager = await VoicesManager.create()

    # Build voice search criteria
    search_criteria = {}
    if gender:
        search_criteria["Gender"] = gender
    if language:
        search_criteria["Language"] = language
    if locale:
        search_criteria["Locale"] = locale

    if not search_criteria:
        raise ValueError(
            "Please provide at least one of 'gender', 'language', or 'locale' for voice selection."
        )

    try:
        # Find matching voices
        matching_voices = voices_manager.find(**search_criteria)
        loggers.debug(matching_voices)
        if not matching_voices:
            raise ValueError(
                f"No voices found matching criteria: {search_criteria}. "
                f"Please check available voices or adjust your criteria."
            )

        # Randomly select one of the matching voices
        selected_voice_name = random.choice(matching_voices)["Name"]
        loggers.info(f"Selected voice: {selected_voice_name} for text: '{text[:30]}...'")

        # Generate audio and get as bytes
        communicate = edge_tts.Communicate(text, selected_voice_name, rate=rate)
        audio_bytes = b""
        async for chunk in communicate.stream():
            if chunk["type"] == "audio":
                audio_bytes += chunk["data"]
        
        # print("Audio bytes and text", text, audio_bytes)

        return audio_bytes

    except ValueError as e:
        # raise e
        loggers.error(e)
    except Exception as e:
        # raise e
        loggers.error(f"Failed to generate audio: {e}")
        # raise Exception(f"Failed to generate audio: {e}")

if __name__ == "__main__":
    import asyncio

    async def main():
        # Example usage:
        text_to_convert = "नमस्ते, यो एक परीक्षण हो।" # "Hello, this is a test." in Nepali
        
        print(f"\nAttempting to generate audio for: '{text_to_convert}'")
        audio_data = await generate_audio_bytes(
            text=text_to_convert,
            language="ne",  # Nepali language
            gender="Female"
        )

        if audio_data:
            output_filename = "output_audio.mp3"
            try:
                with open(output_filename, "wb") as f:
                    f.write(audio_data)
                print(f"Audio successfully saved to {output_filename}")
            except IOError as e:
                print(f"Error saving audio to file: {e}")
        else:
            print("Failed to generate audio.")

        # Another example with different parameters (e.g., male voice, English)
        text_english = "Hello, this is an English test."
        print(f"\nAttempting to generate audio for: '{text_english}'")
        audio_data_en = await generate_audio_bytes(
            text=text_english,
            language="en",
            gender="Male",
            locale="en-US" # Specifying locale for more precise voice selection
        )
        if audio_data_en:
            output_filename_en = "output_audio_en.mp3"
            try:
                with open(output_filename_en, "wb") as f:
                    f.write(audio_data_en)
                print(f"Audio successfully saved to {output_filename_en}")
            except IOError as e:
                print(f"Error saving audio to file: {e}")
        else:
            print("Failed to generate English audio.")

    asyncio.run(main())