# Nepali App Prompt Generation System Documentation

## Overview
This document explains the architecture and flow of the Nepali quiz prompt generation system, focusing on how user data and database-stored prompt templates are combined to create a context-aware prompt for quiz generation.

---

## 1. User Onboarding Data
The system uses the following user-specific onboarding data, fetched from the database:
- **age**: The user's age
- **difficulty_level**: The user's selected quiz difficulty (integer, e.g., 1, 2, 3)
- **preferred_topics**: The user's preferred quiz topics (e.g., ["animals", "colors"])

This data is retrieved from the `users` collection using the user's unique ID.

---

## 2. Prompt Templates in Database
Prompt templates are stored in the `prompts` collection. The following templates are used:

### a. Main Prompt (`main_prompt`)
- The backbone of the quiz prompt, containing placeholders for user data and sub-prompts.
- Example (abridged):
  ```
  You are a Nepali language tutor creating interactive quiz tasks for **{{age}}-year-old children**.
  ...
  {topic_prompt}
  {difficulty_prompt}
  ...
  ```

### b. Topic Guidance (`topic_guidance`)
- Provides instructions on how to use or not use the user's preferred topics.
- Two variants:
  - `topic: existing`: Used if the user has preferred topics. Contains a `{topic}` placeholder.
  - `topic: non_existing`: Used if the user has no preferred topics.

### c. Difficulty Guidance (`difficulty_guidance`)
- Provides instructions based on the user's selected difficulty level.
- One document per difficulty level (e.g., `level: 1`, `level: 2`, `level: 3`).

---

## 3. Prompt Construction Flow

### Step 1: Fetch User Data
- Retrieve `age`, `difficulty_level`, and `preferred_topics` from the user's onboarding document.

### Step 2: Fetch Prompt Templates
- **Difficulty Guidance**: Query `prompts` for `{"name": "difficulty_guidance", "level": <difficulty_level>}`.
- **Topic Guidance**:
  - If `preferred_topics` exists: Query `prompts` for `{"name": "topic_guidance", "topic": "existing"}` and format with the user's topics.
  - If not: Query `prompts` for `{"name": "topic_guidance", "topic": "non_existing"}`.
- **Main Prompt**: Query `prompts` for `{"name": "main_prompt"}`.

### Step 3: Format Sub-Prompts
- **Topic Prompt**: If fetched, format with user's topics (if applicable).
- **Difficulty Prompt**: Use as-is from the database.

### Step 4: Format Main Prompt
- Replace placeholders in the main prompt with:
  - `num_tasks`: Number of quiz tasks requested
  - `difficulty_level`: User's difficulty level
  - `age`: User's age
  - `topic_prompt`: The formatted topic guidance string
  - `difficulty_prompt`: The difficulty guidance string

### Step 5: Return Final Prompt
- The fully formatted prompt string is returned for use in quiz generation.

---

## 4. Example Data and Prompt Construction

### Example User Data
```json
{
  "age": 7,
  "difficulty_level": 1,
  "preferred_topics": ["animals"]
}
```

### Example Prompt Documents
- **main_prompt**: (see your DB for full text)
- **topic_guidance (existing)**: `All tasks must relate naturally to the theme: **"{topic}"**, but should be based only on actual content from the spoken Nepali audio. Do not reuse the topic keyword unless it is heard.`
- **difficulty_guidance (level 1)**: `Use extremely simple vocabulary and short questions. Limit to 2–3 options. Focus on recognizing familiar words or ideas like animals, foods, or family terms.`

### Example Construction
1. Fetch and format `topic_guidance` with `topic = "animals"`.
2. Fetch `difficulty_guidance` for `level = 1`.
3. Fetch `main_prompt` and replace placeholders:
   - `{num_tasks}` → e.g., `3`
   - `{age}` → `7`
   - `{topic_prompt}` → formatted topic guidance
   - `{difficulty_prompt}` → difficulty guidance

**Resulting prompt (abridged):**
```
You are a Nepali language tutor creating interactive quiz tasks for **7-year-old children**.
...
All tasks must relate naturally to the theme: **"animals"**, but should be based only on actual content from the spoken Nepali audio. Do not reuse the topic keyword unless it is heard.
Use extremely simple vocabulary and short questions. Limit to 2–3 options. Focus on recognizing familiar words or ideas like animals, foods, or family terms.
...
```

---

## 5. Complete Picture: Prompt Creation Pipeline

1. **User requests quiz generation** (specifying number of tasks).
2. **System fetches user onboarding data** (age, difficulty, topics).
3. **System fetches prompt templates** from the database.
4. **Sub-prompts (topic, difficulty) are formatted** as needed.
5. **Main prompt is formatted** with all data and sub-prompts.
6. **Final prompt is returned** for use in quiz/task generation.

---

## 6. Notes
- All prompt templates are stored in the database and can be updated without code changes.
- The system is modular: new prompt types or user data fields can be added with minimal changes.
- The prompt formatting relies on Python's `str.format()`; ensure DB templates use single curly braces for placeholders.

---

## 7. Prompts Flow Diagram

![Alt text](sys_flow_prompts.svg)

**End of Documentation**