#!/usr/bin/env python3
"""
Test script to verify the editor service fixes.
"""

def test_string_formatting():
    """Test that the prompt template string formatting works correctly."""
    print("Testing string formatting...")
    
    # Simulate the prompt template with escaped JSON
    prompt_template = '''
You are a Nepali tutor creating a fun, interactive **story-based quiz** for **{age}-year-old children**.

```json
"engagement_questions": [
  {{{{
    "text": "Question in Nepali asking to discuss with family",
    "text_en": "English translation",
    "type": "family_discussion"
  }}}}
]
```

Generate {num_tasks} tasks.
'''
    
    try:
        final_prompt = prompt_template.format(age=10, num_tasks=5)
        print("✅ SUCCESS: String formatting works correctly")
        print("First 200 chars:", final_prompt[:200])
        return True
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_field_extraction():
    """Test that field extraction from AI response works correctly."""
    print("\nTesting field extraction...")
    
    # Simulate the AI response structure
    generated_tasks = {
        'title': 'सानी लक्ष्मीको जादुई ढुङ्गा',
        'title_en': "Little Laxmi's Magical Stone",
        'thumbnail': 'Magical Stone',
        'description': 'गरिबीमा बाँचिरहेकी लक्ष्मीको कथा',
        'description_en': 'The story of Laxmi living in poverty',
        'engagement_questions': [
            {
                'text': 'आफ्ना बुबालाई चाडपर्वको महत्वको बारेमा सोध्नुहोस्',
                'text_en': 'Ask your father about the importance of festivals',
                'type': 'family_discussion'
            }
        ],
        'output': [
            {
                'title': 'सानी लक्ष्मीको रमाइलो यात्रा',
                'tasks': [
                    {
                        'title': 'सपनाको सुरुवात',
                        'type': 'single_choice',
                        'story': {
                            'stage': 1,
                            'script': 'धेरै वर्ष अगाडिको कुरा हो...',
                            'image': 'A small house in a village',
                            'audio': 'घर'
                        },
                        'question': {
                            'type': 'single_choice',
                            'text': 'आमाको नाम के थियो?',
                            'translated_text': "What was the mother's name?",
                            'options': {'a': 'लक्ष्मी', 'b': 'पार्वती', 'c': 'सीता'},
                            'answer_hint': 'Parvati',
                            'answer': 'b'
                        },
                        'max_score': 10,
                        'complexity': 1
                    }
                ]
            }
        ]
    }
    
    # Test field extraction
    try:
        title = generated_tasks.get("title", "Generated Curated Content 1")
        title_en = generated_tasks.get("title_en", "")
        description = generated_tasks.get("description", "")
        description_en = generated_tasks.get("description_en", "")
        engagement_questions = generated_tasks.get("engagement_questions", [])
        thumbnail = generated_tasks.get("thumbnail", title)
        
        print(f"✅ Title: {title}")
        print(f"✅ Title EN: {title_en}")
        print(f"✅ Description: {description}")
        print(f"✅ Description EN: {description_en}")
        print(f"✅ Thumbnail: {thumbnail}")
        print(f"✅ Engagement Questions: {len(engagement_questions)} found")
        
        # Test task extraction
        tasks_data = generated_tasks.get("output")
        if tasks_data and isinstance(tasks_data, list):
            tasks = []
            for chapter in tasks_data:
                if isinstance(chapter, dict) and "tasks" in chapter:
                    chapter_tasks = chapter["tasks"]
                    if isinstance(chapter_tasks, list):
                        tasks.extend(chapter_tasks)
            
            print(f"✅ Tasks extracted: {len(tasks)} tasks found")
            return True
        else:
            print("❌ ERROR: No tasks data found")
            return False
            
    except Exception as e:
        print(f"❌ ERROR in field extraction: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Editor Service Fixes")
    print("=" * 50)
    
    test1_passed = test_string_formatting()
    test2_passed = test_field_extraction()
    
    print("\n" + "=" * 50)
    if test1_passed and test2_passed:
        print("🎉 ALL TESTS PASSED! The fixes should work correctly.")
    else:
        print("❌ Some tests failed. Please check the issues above.")
